#!/usr/bin/env swift

import Foundation

// MARK: - Simple TMDB API Tester
// This is a standalone script to test TMDB API functionality

struct SimpleTMDBTester {
    private let baseURL = "https://api.themoviedb.org/3"
    private var apiKey: String = ""
    
    init() {
        // Try to get API key from environment or prompt user
        self.apiKey = getAPIKey()
    }
    
    // MARK: - API Key Management
    private func getAPIKey() -> String {
        // Check environment variable first
        if let envKey = ProcessInfo.processInfo.environment["TMDB_API_KEY"], !envKey.isEmpty {
            print("✅ Found TMDB API key in environment variable")
            return envKey
        }
        
        // Check for .local.env file
        if let envFileKey = readFromEnvFile() {
            print("✅ Found TMDB API key in .local.env file")
            return envFileKey
        }
        
        // Prompt user for API key
        print("🔑 TMDB API key not found in environment.")
        print("Please enter your TMDB API key (or press Enter to use test key):")
        
        if let input = readLine(), !input.isEmpty {
            return input
        }
        
        print("⚠️  No API key provided. Using placeholder (tests will fail)")
        return "YOUR_TMDB_API_KEY"
    }
    
    private func readFromEnvFile() -> String? {
        let envFilePath = ".local.env"
        
        guard let content = try? String(contentsOfFile: envFilePath, encoding: .utf8) else {
            return nil
        }
        
        let lines = content.components(separatedBy: .newlines)
        for line in lines {
            let trimmed = line.trimmingCharacters(in: .whitespaces)
            if trimmed.hasPrefix("TMDB_API_KEY=") {
                let key = String(trimmed.dropFirst("TMDB_API_KEY=".count))
                return key.trimmingCharacters(in: .whitespaces)
            }
        }
        
        return nil
    }
    
    // MARK: - Test Functions
    func runAllTests() async {
        print("🎬 TMDB API Test Suite")
        print("=" + String(repeating: "=", count: 49))
        
        // Validate API key
        guard validateAPIKey() else {
            print("❌ Invalid API key. Cannot proceed with tests.")
            return
        }
        
        // Run tests
        await testBasicConnectivity()
        await testPopularMovies()
        await testTrendingMovies()
        await testMovieDetails()
        await testSearchFunctionality()
        await testTVShows()
        
        print("=" + String(repeating: "=", count: 49))
        print("🎬 Tests Complete!")
    }
    
    private func validateAPIKey() -> Bool {
        print("\n🔑 Validating API Key...")
        
        if apiKey == "YOUR_TMDB_API_KEY" || apiKey.isEmpty {
            print("❌ API key not configured")
            return false
        }
        
        if apiKey.count < 20 {
            print("⚠️  API key seems too short (length: \(apiKey.count))")
        }
        
        print("✅ API key configured (length: \(apiKey.count))")
        return true
    }
    
    private func testBasicConnectivity() async {
        print("\n🌐 Testing Basic Connectivity...")
        
        let url = "\(baseURL)/configuration?api_key=\(apiKey)"
        
        do {
            let (data, response) = try await URLSession.shared.data(from: URL(string: url)!)
            
            if let httpResponse = response as? HTTPURLResponse {
                print("✅ HTTP Status: \(httpResponse.statusCode)")
                
                if httpResponse.statusCode == 200 {
                    print("✅ Successfully connected to TMDB API")
                } else if httpResponse.statusCode == 401 {
                    print("❌ Authentication failed - check your API key")
                } else {
                    print("⚠️  Unexpected status code: \(httpResponse.statusCode)")
                }
            }
            
            // Try to parse response
            if let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
                if let images = json["images"] as? [String: Any],
                   let baseURL = images["base_url"] as? String {
                    print("✅ API response valid - Image base URL: \(baseURL)")
                }
            }
            
        } catch {
            print("❌ Connection failed: \(error)")
        }
    }
    
    private func testPopularMovies() async {
        print("\n🎥 Testing Popular Movies...")
        
        let url = "\(baseURL)/movie/popular?api_key=\(apiKey)"
        
        do {
            let (data, _) = try await URLSession.shared.data(from: URL(string: url)!)
            
            if let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
               let results = json["results"] as? [[String: Any]] {
                
                print("✅ Retrieved \(results.count) popular movies")
                
                if let firstMovie = results.first,
                   let title = firstMovie["title"] as? String,
                   let rating = firstMovie["vote_average"] as? Double {
                    print("   Example: '\(title)' (Rating: \(rating))")
                }
            } else {
                print("❌ Failed to parse popular movies response")
            }
            
        } catch {
            print("❌ Popular movies request failed: \(error)")
        }
    }
    
    private func testTrendingMovies() async {
        print("\n📈 Testing Trending Movies...")
        
        let url = "\(baseURL)/trending/movie/week?api_key=\(apiKey)"
        
        do {
            let (data, _) = try await URLSession.shared.data(from: URL(string: url)!)
            
            if let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
               let results = json["results"] as? [[String: Any]] {
                
                print("✅ Retrieved \(results.count) trending movies")
                
                if let firstMovie = results.first,
                   let title = firstMovie["title"] as? String {
                    print("   Top trending: '\(title)'")
                }
            } else {
                print("❌ Failed to parse trending movies response")
            }
            
        } catch {
            print("❌ Trending movies request failed: \(error)")
        }
    }
    
    private func testMovieDetails() async {
        print("\n🎬 Testing Movie Details...")
        
        // Use a well-known movie ID (Inception)
        let movieId = 27205
        let url = "\(baseURL)/movie/\(movieId)?api_key=\(apiKey)"
        
        do {
            let (data, _) = try await URLSession.shared.data(from: URL(string: url)!)
            
            if let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
               let title = json["title"] as? String,
               let runtime = json["runtime"] as? Int {
                
                print("✅ Movie details retrieved")
                print("   Title: \(title)")
                print("   Runtime: \(runtime) minutes")
                
                if let genres = json["genres"] as? [[String: Any]] {
                    let genreNames = genres.compactMap { $0["name"] as? String }
                    print("   Genres: \(genreNames.joined(separator: ", "))")
                }
            } else {
                print("❌ Failed to parse movie details response")
            }
            
        } catch {
            print("❌ Movie details request failed: \(error)")
        }
    }
    
    private func testSearchFunctionality() async {
        print("\n🔍 Testing Search...")
        
        let query = "Inception"
        let encodedQuery = query.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? query
        let url = "\(baseURL)/search/multi?api_key=\(apiKey)&query=\(encodedQuery)"
        
        do {
            let (data, _) = try await URLSession.shared.data(from: URL(string: url)!)
            
            if let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
               let results = json["results"] as? [[String: Any]] {
                
                print("✅ Search for '\(query)' returned \(results.count) results")
                
                let movies = results.filter { ($0["media_type"] as? String) == "movie" }
                let tvShows = results.filter { ($0["media_type"] as? String) == "tv" }
                let people = results.filter { ($0["media_type"] as? String) == "person" }
                
                print("   Movies: \(movies.count), TV Shows: \(tvShows.count), People: \(people.count)")
            } else {
                print("❌ Failed to parse search response")
            }
            
        } catch {
            print("❌ Search request failed: \(error)")
        }
    }
    
    private func testTVShows() async {
        print("\n📺 Testing TV Shows...")
        
        let url = "\(baseURL)/tv/popular?api_key=\(apiKey)"
        
        do {
            let (data, _) = try await URLSession.shared.data(from: URL(string: url)!)
            
            if let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
               let results = json["results"] as? [[String: Any]] {
                
                print("✅ Retrieved \(results.count) popular TV shows")
                
                if let firstShow = results.first,
                   let name = firstShow["name"] as? String {
                    print("   Example: '\(name)'")
                }
            } else {
                print("❌ Failed to parse TV shows response")
            }
            
        } catch {
            print("❌ TV shows request failed: \(error)")
        }
    }
}

// MARK: - Main Execution
let tester = SimpleTMDBTester()
await tester.runAllTests()
