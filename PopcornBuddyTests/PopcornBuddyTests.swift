//
//  PopcornBuddyTests.swift
//  PopcornBuddyTests
//
//  Created by BINGQI LIU on 5/30/25.
//

import Testing
import Foundation
@testable import PopcornBuddy

struct PopcornBuddyTests {

    @Test func example() async throws {
        // Write your test here and use APIs like `#expect(...)` to check expected conditions.
    }

    // MARK: - TMDB API Tests
    @Test("TMDB API Key Configuration")
    func testTMDBAPIKeyConfiguration() async throws {
        let configManager = ConfigurationManager.shared
        let apiKey = configManager.tmdbAPIKey

        // Check if API key is configured
        #expect(!apiKey.isEmpty, "TMDB API key should not be empty")
        #expect(apiKey != "YOUR_TMDB_API_KEY", "TMDB API key should be configured with actual key")

        // Check if configuration manager reports valid keys
        if configManager.hasValidAPIKeys() {
            print("✅ Configuration manager reports valid API keys")
        } else {
            print("⚠️ Configuration manager reports invalid API keys - this may be expected if OpenAI key is not set")
        }
    }

    @Test("TMDB API Basic Connectivity")
    func testTMDBBasicConnectivity() async throws {
        let tmdbService = TMDBService.shared

        do {
            let movies = try await tmdbService.fetchPopularMovies()
            #expect(!movies.isEmpty, "Should retrieve popular movies")
            print("✅ Successfully retrieved \(movies.count) popular movies")

            if let firstMovie = movies.first {
                print("   Example movie: \(firstMovie.title)")
                #expect(!firstMovie.title.isEmpty, "Movie title should not be empty")
                #expect(firstMovie.id > 0, "Movie ID should be positive")
            }
        } catch {
            print("❌ TMDB API test failed: \(error)")
            throw error
        }
    }

    @Test("TMDB Movie Search")
    func testTMDBMovieSearch() async throws {
        let tmdbService = TMDBService.shared

        do {
            let searchResults = try await tmdbService.searchMulti(query: "Inception")
            #expect(!searchResults.isEmpty, "Search should return results for 'Inception'")
            print("✅ Search returned \(searchResults.count) results")

            // Check if we have the expected movie in results
            let inceptionMovie = searchResults.first { result in
                result.displayTitle.lowercased().contains("inception") && result.mediaType == "movie"
            }

            #expect(inceptionMovie != nil, "Should find Inception movie in search results")

            if let movie = inceptionMovie {
                print("   Found: \(movie.displayTitle) (ID: \(movie.id))")
            }
        } catch {
            print("❌ TMDB search test failed: \(error)")
            throw error
        }
    }

    @Test("TMDB Movie Details")
    func testTMDBMovieDetails() async throws {
        let tmdbService = TMDBService.shared

        // Test with Inception movie ID
        let inceptionId = 27205

        do {
            let movieDetails = try await tmdbService.fetchMovieDetails(id: inceptionId)

            #expect(movieDetails.id == inceptionId, "Movie ID should match requested ID")
            #expect(movieDetails.title.lowercased().contains("inception"), "Should be Inception movie")
            #expect(movieDetails.runtime != nil, "Runtime should be available")

            print("✅ Movie details retrieved:")
            print("   Title: \(movieDetails.title)")
            print("   Runtime: \(movieDetails.runtime ?? 0) minutes")
            print("   Genres: \(movieDetails.genres.map { $0.name }.joined(separator: ", "))")

        } catch {
            print("❌ TMDB movie details test failed: \(error)")
            throw error
        }
    }

    @Test("TMDB TV Shows")
    func testTMDBTVShows() async throws {
        let tmdbService = TMDBService.shared

        do {
            let tvShows = try await tmdbService.fetchPopularTVShows()
            #expect(!tvShows.isEmpty, "Should retrieve popular TV shows")
            print("✅ Successfully retrieved \(tvShows.count) popular TV shows")

            if let firstShow = tvShows.first {
                print("   Example TV show: \(firstShow.name)")
                #expect(!firstShow.name.isEmpty, "TV show name should not be empty")
                #expect(firstShow.id > 0, "TV show ID should be positive")
            }
        } catch {
            print("❌ TMDB TV shows test failed: \(error)")
            throw error
        }
    }

    @Test("TMDB Image URL Generation")
    func testTMDBImageURLGeneration() async throws {
        let tmdbService = TMDBService.shared

        let testPosterPath = "/example_poster.jpg"
        let testBackdropPath = "/example_backdrop.jpg"

        // Test poster URL generation
        let posterURL = tmdbService.posterURL(for: testPosterPath, size: .w500)
        #expect(posterURL != nil, "Should generate poster URL")
        #expect(posterURL?.absoluteString.contains("image.tmdb.org") == true, "Should use TMDB image domain")
        #expect(posterURL?.absoluteString.contains("w500") == true, "Should include size parameter")

        // Test backdrop URL generation
        let backdropURL = tmdbService.backdropURL(for: testBackdropPath, size: .w1280)
        #expect(backdropURL != nil, "Should generate backdrop URL")
        #expect(backdropURL?.absoluteString.contains("image.tmdb.org") == true, "Should use TMDB image domain")
        #expect(backdropURL?.absoluteString.contains("w1280") == true, "Should include size parameter")

        // Test nil handling
        let nilPosterURL = tmdbService.posterURL(for: nil)
        let nilBackdropURL = tmdbService.backdropURL(for: nil)
        #expect(nilPosterURL == nil, "Should return nil for nil poster path")
        #expect(nilBackdropURL == nil, "Should return nil for nil backdrop path")

        print("✅ Image URL generation working correctly")
        if let url = posterURL {
            print("   Example poster URL: \(url)")
        }
    }

}
