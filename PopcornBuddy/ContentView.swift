import SwiftUI
import CoreData

struct ContentView: View {
    @State private var selectedTab = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            HomeView()
                .tabItem {
                    Image(systemName: "house.fill")
                    Text("Home")
                }
                .tag(0)
            
            MoviesView()
                .tabItem {
                    Image(systemName: "film.fill")
                    Text("Movies")
                }
                .tag(1)
            
            TVShowsView()
                .tabItem {
                    Image(systemName: "tv.fill")
                    Text("TV Shows")
                }
                .tag(2)
            
            EnhancedSearchView()
                .tabItem {
                    Image(systemName: "magnifyingglass")
                    Text("Search")
                }
                .tag(3)
            
            PlaceholderView(title: "Daily Recommendations")
                .tabItem {
                    Image(systemName: "calendar.badge.clock")
                    Text("Daily")
                }
                .tag(4)

            PlaceholderView(title: "AI Recommendations")
                .tabItem {
                    Image(systemName: "brain.head.profile")
                    Text("AI")
                }
                .tag(5)
            
            ProfileView()
                .tabItem {
                    Image(systemName: "person.fill")
                    Text("Profile")
                }
                .tag(6)
        }
        .accentColor(.blue)
    }
    
    // MARK: - Home View
    struct HomeView: View {
        @StateObject private var sampleData = SampleDataManager.shared

        var body: some View {
            NavigationView {
                ScrollView {
                    VStack(spacing: 24) {
                        // Welcome Header
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                VStack(alignment: .leading) {
                                    Text("Welcome back!")
                                        .font(.title2)
                                        .fontWeight(.medium)
                                    
                                    Text("Continue watching or discover something new")
                                        .font(.subheadline)
                                        .foregroundColor(.secondary)
                                }
                                
                                Spacer()
                                
                                Button(action: {}) {
                                    Image(systemName: "bell")
                                        .font(.title2)
                                        .foregroundColor(.primary)
                                }
                            }
                            .padding(.horizontal)
                        }
                        
                        // Continue Watching Section
                        SimpleCarouselSection(
                            title: "Continue Watching",
                            subtitle: "Pick up where you left off"
                        ) {
                            // Placeholder for continue watching items
                            ForEach(0..<5, id: \.self) { _ in
                                ContinueWatchingCard()
                            }
                        }

                        // Trending Section
                        SimpleCarouselSection(
                            title: "Trending Now",
                            subtitle: "What everyone's watching"
                        ) {
                            ForEach(sampleData.trendingContent) { item in
                                MediaCard(item: item)
                            }
                        }

                        // Recently Added Section
                        SimpleCarouselSection(
                            title: "Recently Added",
                            subtitle: "New to your watchlist"
                        ) {
                            ForEach(sampleData.recentlyAdded) { item in
                                MediaCard(item: item)
                            }
                        }
                    }
                    .padding(.top)
                }
                .navigationTitle("")
            }
        }
    }
    
    // MARK: - Movies View
    struct MoviesView: View {
        @StateObject private var sampleData = SampleDataManager.shared

        var body: some View {
            NavigationView {
                ScrollView {
                    VStack(spacing: 20) {
                        Text("Movies")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .padding()
                        
                        // Popular Movies Section
                        VStack(alignment: .leading, spacing: 12) {
                            HStack {
                                Text("Popular Movies")
                                    .font(.title2)
                                    .fontWeight(.semibold)
                                Spacer()
                            }
                            .padding(.horizontal)
                            
                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack(spacing: 16) {
                                    ForEach(sampleData.popularMovies) { item in
                                        MediaCard(item: item)
                                    }
                                }
                                .padding(.horizontal)
                            }
                        }
                        
                        // Top Rated Movies Section
                        VStack(alignment: .leading, spacing: 12) {
                            HStack {
                                Text("Top Rated")
                                    .font(.title2)
                                    .fontWeight(.semibold)
                                Spacer()
                            }
                            .padding(.horizontal)
                            
                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack(spacing: 16) {
                                    ForEach(sampleData.topRatedMovies) { item in
                                        MediaCard(item: item)
                                    }
                                }
                                .padding(.horizontal)
                            }
                        }
                    }
                }
                .navigationTitle("Movies")
            }
        }
    }
    
    // MARK: - TV Shows View
    struct TVShowsView: View {
        @StateObject private var sampleData = SampleDataManager.shared

        var body: some View {
            NavigationView {
                ScrollView {
                    VStack(spacing: 20) {
                        Text("TV Shows")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .padding()
                        
                        // Popular TV Shows Section
                        VStack(alignment: .leading, spacing: 12) {
                            HStack {
                                Text("Popular Shows")
                                    .font(.title2)
                                    .fontWeight(.semibold)
                                Spacer()
                            }
                            .padding(.horizontal)
                            
                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack(spacing: 16) {
                                    ForEach(sampleData.popularTVShows) { item in
                                        MediaCard(item: item)
                                    }
                                }
                                .padding(.horizontal)
                            }
                        }
                        
                        // Trending Shows Section
                        VStack(alignment: .leading, spacing: 12) {
                            HStack {
                                Text("Trending Shows")
                                    .font(.title2)
                                    .fontWeight(.semibold)
                                Spacer()
                            }
                            .padding(.horizontal)
                            
                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack(spacing: 16) {
                                    ForEach(sampleData.trendingShows) { item in
                                        MediaCard(item: item)
                                    }
                                }
                                .padding(.horizontal)
                            }
                        }
                    }
                }
                .navigationTitle("TV Shows")
            }
        }
    }

    // MARK: - Profile View
    struct ProfileView: View {
        var body: some View {
            NavigationView {
                VStack(spacing: 30) {
                    // Profile Header
                    VStack(spacing: 16) {
                        Circle()
                            .fill(Color.blue.opacity(0.2))
                            .frame(width: 100, height: 100)
                            .overlay(
                                Image(systemName: "person.fill")
                                    .font(.system(size: 40))
                                    .foregroundColor(.blue)
                            )
                        
                        Text("Movie Enthusiast")
                            .font(.title2)
                            .fontWeight(.semibold)
                    }
                    
                    // Stats Section
                    HStack(spacing: 40) {
                        VStack {
                            Text("42")
                                .font(.title)
                                .fontWeight(.bold)
                            Text("Movies")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        VStack {
                            Text("18")
                                .font(.title)
                                .fontWeight(.bold)
                            Text("TV Shows")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        VStack {
                            Text("8.2")
                                .font(.title)
                                .fontWeight(.bold)
                            Text("Avg Rating")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    // Settings List
                    VStack(spacing: 0) {
                        NavigationLink(destination: PlaceholderView(title: "iCloud Sync")) {
                            ProfileRow(icon: "icloud", title: "iCloud Sync", showChevron: true)
                        }
                        .buttonStyle(PlainButtonStyle())

                        NavigationLink(destination: PlaceholderView(title: "Notifications")) {
                            ProfileRow(icon: "bell", title: "Notifications", showChevron: true)
                        }
                        .buttonStyle(PlainButtonStyle())
                        ProfileRow(icon: "moon", title: "Dark Mode", showChevron: true)
                        ProfileRow(icon: "gear", title: "Settings", showChevron: true)
                        ProfileRow(icon: "questionmark.circle", title: "Help & Support", showChevron: true)
                    }
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(12)
                    .padding(.horizontal)
                    
                    Spacer()
                }
                .padding(.top, 20)
                .navigationTitle("Profile")
            }
        }
    }
    
    struct ProfileRow: View {
        let icon: String
        let title: String
        let showChevron: Bool
        
        var body: some View {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(.blue)
                    .frame(width: 24)
                
                Text(title)
                    .font(.body)
                
                Spacer()
                
                if showChevron {
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding()
            .background(Color.white)
        }
    }
    
    // MARK: - Supporting Views

    struct MediaCard: View {
        let item: MediaDisplayItem

        var body: some View {
            NavigationLink(destination: DetailView(mediaItem: item.toMediaItem())) {
                VStack(alignment: .leading, spacing: 8) {
                    // Poster Image
                    AsyncImage(url: item.posterURL) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    } placeholder: {
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.gray.opacity(0.3))
                            .overlay(
                                ProgressView()
                                    .scaleEffect(0.8)
                            )
                    }
                    .frame(width: 120, height: 180)
                    .clipShape(RoundedRectangle(cornerRadius: 12))

                    // Title and Year
                    VStack(alignment: .leading, spacing: 2) {
                        Text(item.title)
                            .font(.caption)
                            .fontWeight(.medium)
                            .lineLimit(2)
                            .multilineTextAlignment(.leading)

                        HStack {
                            Text(item.year)
                                .font(.caption2)
                                .foregroundColor(.secondary)

                            Spacer()

                            // Rating
                            HStack(spacing: 2) {
                                Image(systemName: "star.fill")
                                    .font(.caption2)
                                    .foregroundColor(.yellow)
                                Text(String(format: "%.1f", item.rating))
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                }
                .frame(width: 120)
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
    
    struct ContinueWatchingCard: View {
        var body: some View {
            VStack(alignment: .leading, spacing: 8) {
                ZStack(alignment: .bottomLeading) {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.gray.opacity(0.3))
                        .frame(width: 200, height: 120)
                        .overlay(
                            Image(systemName: "play.circle.fill")
                                .font(.title)
                                .foregroundColor(.white)
                        )
                    
                    // Progress bar
                    VStack {
                        Spacer()
                        ProgressView(value: 0.6)
                            .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                            .scaleEffect(x: 1, y: 0.5)
                            .padding(.horizontal, 8)
                            .padding(.bottom, 8)
                    }
                }
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("Show Title")
                        .font(.caption)
                        .fontWeight(.medium)
                        .lineLimit(1)
                    
                    Text("S1 E5 • 23 min left")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
            .frame(width: 200)
        }
    }
    
    // MARK: - Simple Carousel Section
    struct SimpleCarouselSection<Content: View>: View {
        let title: String
        let subtitle: String
        let content: Content

        init(title: String, subtitle: String, @ViewBuilder content: () -> Content) {
            self.title = title
            self.subtitle = subtitle
            self.content = content()
        }

        var body: some View {
            VStack(alignment: .leading, spacing: 16) {
                // Section header
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)

                    Text(subtitle)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding(.horizontal)

                // Horizontal scrolling content
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 16) {
                        content
                    }
                    .padding(.horizontal)
                }
            }
        }
    }
}

// MARK: - Placeholder View
struct PlaceholderView: View {
    let title: String

    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "gear")
                .font(.system(size: 50))
                .foregroundColor(.gray)

            Text(title)
                .font(.title2)
                .fontWeight(.semibold)

            Text("This feature is coming soon!")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
        .navigationTitle(title)
        .navigationBarTitleDisplayMode(.inline)
    }
}

#Preview {
    ContentView()
}
