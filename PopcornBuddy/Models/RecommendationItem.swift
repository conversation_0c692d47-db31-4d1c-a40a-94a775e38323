import Foundation

// MARK: - Recommendation Item Model
struct RecommendationItem: Identifiable, Codable {
    let id: Int
    let title: String
    let description: String
    let type: RecommendationType
    let aiGenerated: Bool
    let confidence: Double
    let tmdbID: Int?
    let posterPath: String?
    let backdropPath: String?
    let releaseYear: String?
    let genres: [String]
    let rating: Double?
    
    init(
        id: Int,
        title: String,
        description: String,
        type: RecommendationType,
        aiGenerated: Bool,
        confidence: Double,
        tmdbID: Int? = nil,
        posterPath: String? = nil,
        backdropPath: String? = nil,
        releaseYear: String? = nil,
        genres: [String] = [],
        rating: Double? = nil
    ) {
        self.id = id
        self.title = title
        self.description = description
        self.type = type
        self.aiGenerated = aiGenerated
        self.confidence = confidence
        self.tmdbID = tmdbID
        self.posterPath = posterPath
        self.backdropPath = backdropPath
        self.releaseYear = releaseYear
        self.genres = genres
        self.rating = rating
    }
    
    // MARK: - Computed Properties
    var displayTitle: String {
        if let year = releaseYear {
            return "\(title) (\(year))"
        }
        return title
    }
    
    var confidenceText: String {
        let percentage = Int(confidence * 100)
        return "\(percentage)% match"
    }
    
    var confidenceColor: String {
        switch confidence {
        case 0.8...1.0:
            return "green"
        case 0.6..<0.8:
            return "orange"
        default:
            return "red"
        }
    }
    
    var typeIcon: String {
        switch type {
        case .movie:
            return "film"
        case .tvShow:
            return "tv"
        }
    }
    
    var aiGeneratedBadge: String {
        return aiGenerated ? "✨ AI Recommended" : "📈 Trending"
    }
    
    // MARK: - Helper Methods
    func posterURL(size: ImageSize = .w500) -> URL? {
        guard let posterPath = posterPath else { return nil }
        return TMDBService.shared.posterURL(for: posterPath, size: size)
    }

    func backdropURL(size: ImageSize = .w1280) -> URL? {
        guard let backdropPath = backdropPath else { return nil }
        return TMDBService.shared.backdropURL(for: backdropPath, size: size)
    }
}

// MARK: - Recommendation Type
enum RecommendationType: String, Codable, CaseIterable {
    case movie = "movie"
    case tvShow = "tv_show"
    
    var displayName: String {
        switch self {
        case .movie:
            return "Movie"
        case .tvShow:
            return "TV Show"
        }
    }
    
    var pluralName: String {
        switch self {
        case .movie:
            return "Movies"
        case .tvShow:
            return "TV Shows"
        }
    }
}



// MARK: - Enhanced Recommendation Item
struct EnhancedRecommendationItem: Identifiable {
    let id = UUID()
    let recommendationItem: RecommendationItem
    let tmdbDetails: TMDBContentDetails?
    let personalizedReason: String
    let similarToWatched: [String]
    
    var title: String { recommendationItem.title }
    var description: String { recommendationItem.description }
    var confidence: Double { recommendationItem.confidence }
    var type: RecommendationType { recommendationItem.type }
    
    // Enhanced properties from TMDB
    var enhancedOverview: String {
        return tmdbDetails?.overview ?? description
    }
    
    var cast: [String] {
        return tmdbDetails?.cast ?? []
    }
    
    var director: String? {
        return tmdbDetails?.director
    }
    
    var runtime: Int? {
        return tmdbDetails?.runtime
    }
    
    var voteAverage: Double? {
        return tmdbDetails?.voteAverage
    }
}

// MARK: - TMDB Content Details
struct TMDBContentDetails {
    let overview: String
    let cast: [String]
    let director: String?
    let runtime: Int?
    let voteAverage: Double
    let voteCount: Int
    let popularity: Double
    let originalLanguage: String
    let productionCountries: [String]
}

// MARK: - Recommendation Categories
enum RecommendationCategory: String, CaseIterable {
    case aiPersonalized = "ai_personalized"
    case trending = "trending"
    case similarToWatched = "similar_to_watched"
    case newReleases = "new_releases"
    case topRated = "top_rated"
    case genreBased = "genre_based"
    
    var displayName: String {
        switch self {
        case .aiPersonalized:
            return "AI Recommendations"
        case .trending:
            return "Trending Now"
        case .similarToWatched:
            return "More Like What You've Watched"
        case .newReleases:
            return "New Releases"
        case .topRated:
            return "Top Rated"
        case .genreBased:
            return "Based on Your Favorite Genres"
        }
    }
    
    var subtitle: String {
        switch self {
        case .aiPersonalized:
            return "Powered by AI based on your viewing history"
        case .trending:
            return "What everyone's watching right now"
        case .similarToWatched:
            return "Content similar to your favorites"
        case .newReleases:
            return "Recently released content"
        case .topRated:
            return "Highest rated content"
        case .genreBased:
            return "Matches your preferred genres"
        }
    }
    
    var icon: String {
        switch self {
        case .aiPersonalized:
            return "brain.head.profile"
        case .trending:
            return "chart.line.uptrend.xyaxis"
        case .similarToWatched:
            return "heart.text.square"
        case .newReleases:
            return "sparkles"
        case .topRated:
            return "star.fill"
        case .genreBased:
            return "tag.fill"
        }
    }
}

// MARK: - Recommendation Response
struct RecommendationResponse {
    let movies: [RecommendationItem]
    let tvShows: [RecommendationItem]
    let insights: String
    let categories: [RecommendationCategory: [RecommendationItem]]
    let generatedAt: Date
    let userProfileSummary: String
    
    var totalRecommendations: Int {
        return movies.count + tvShows.count
    }
    
    var aiGeneratedCount: Int {
        return movies.filter(\.aiGenerated).count + tvShows.filter(\.aiGenerated).count
    }
    
    var averageConfidence: Double {
        let allItems = movies + tvShows
        guard !allItems.isEmpty else { return 0.0 }
        return allItems.map(\.confidence).reduce(0, +) / Double(allItems.count)
    }
}

// MARK: - Recommendation Filters
struct RecommendationFilters {
    var types: Set<RecommendationType> = Set(RecommendationType.allCases)
    var categories: Set<RecommendationCategory> = Set(RecommendationCategory.allCases)
    var minConfidence: Double = 0.0
    var maxConfidence: Double = 1.0
    var aiGeneratedOnly: Bool = false
    var genres: Set<String> = []
    var yearRange: ClosedRange<Int>?
    var minRating: Double?
    
    func matches(_ item: RecommendationItem) -> Bool {
        // Type filter
        guard types.contains(item.type) else { return false }
        
        // Confidence filter
        guard item.confidence >= minConfidence && item.confidence <= maxConfidence else { return false }
        
        // AI generated filter
        if aiGeneratedOnly && !item.aiGenerated { return false }
        
        // Genre filter
        if !genres.isEmpty {
            let itemGenres = Set(item.genres.map { $0.lowercased() })
            let filterGenres = Set(genres.map { $0.lowercased() })
            guard !itemGenres.isDisjoint(with: filterGenres) else { return false }
        }
        
        // Year filter
        if let yearRange = yearRange,
           let releaseYear = item.releaseYear,
           let year = Int(releaseYear) {
            guard yearRange.contains(year) else { return false }
        }
        
        // Rating filter
        if let minRating = minRating,
           let rating = item.rating {
            guard rating >= minRating else { return false }
        }
        
        return true
    }
}
