import Foundation

// MARK: - Media Display Item
struct MediaDisplayItem: Identifiable {
    let id: Int
    let title: String
    let year: String
    let rating: Double
    let overview: String
    let posterURL: URL?
    let backdropURL: URL?
    let type: MediaType
    let runtime: Int?
    let genres: [String]
    
    enum MediaType {
        case movie
        case tvShow
    }
    
    // Convert to MediaItem for DetailView compatibility
    func toMediaItem() -> MediaItem {
        return MediaItem(
            id: id,
            title: title,
            overview: overview,
            posterURL: posterURL,
            backdropURL: backdropURL,
            rating: rating,
            year: year,
            runtime: runtime
        )
    }
}

// MARK: - Sample Data Manager
class SampleDataManager: ObservableObject {
    static let shared = SampleDataManager()
    
    private init() {}
    
    // MARK: - Popular Movies (Recent 2024)
    let popularMovies: [MediaDisplayItem] = [
        MediaDisplayItem(
            id: 693134,
            title: "Dune: Part Two",
            year: "2024",
            rating: 8.2,
            overview: "Follow the mythic journey of <PERSON> as he unites with <PERSON><PERSON> and the Fremen while on a path of revenge against the conspirators who destroyed his family.",
            posterURL: URL(string: "https://image.tmdb.org/t/p/w500/1pdfLvkbY9ohJlCjQH2CZjjYVvJ.jpg"),
            backdropURL: URL(string: "https://image.tmdb.org/t/p/w1280/xOMo8BRK7PfcJv9JCnx7s5hj0PX.jpg"),
            type: .movie,
            runtime: 166,
            genres: ["Science Fiction", "Adventure", "Drama"]
        ),
        MediaDisplayItem(
            id: 533535,
            title: "Deadpool & Wolverine",
            year: "2024",
            rating: 7.8,
            overview: "A listless Wade Wilson toils away in civilian life with his days as the morally flexible mercenary, Deadpool, behind him. But when his homeworld faces an existential threat, Wade must reluctantly suit-up again with an even more reluctant Wolverine.",
            posterURL: URL(string: "https://image.tmdb.org/t/p/w500/8cdWjvZQUExUUTzyp4t6EDMubfO.jpg"),
            backdropURL: URL(string: "https://image.tmdb.org/t/p/w1280/yDHYTfA3R0jFYba16jBB1ef8oIt.jpg"),
            type: .movie,
            runtime: 128,
            genres: ["Action", "Comedy", "Science Fiction"]
        ),
        MediaDisplayItem(
            id: 1022789,
            title: "Inside Out 2",
            year: "2024",
            rating: 7.6,
            overview: "Teenager Riley's mind headquarters is undergoing a sudden demolition to make room for something entirely unexpected: new Emotions! Joy, Sadness, Anger, Fear and Disgust, who've long been running a successful operation by all accounts, aren't sure how to feel when Anxiety shows up.",
            posterURL: URL(string: "https://image.tmdb.org/t/p/w500/vpnVM9B6NMmQpWeZvzLvDESb2QY.jpg"),
            backdropURL: URL(string: "https://image.tmdb.org/t/p/w1280/stKGOm8UyhuLPR9sZLjs5AkmncA.jpg"),
            type: .movie,
            runtime: 96,
            genres: ["Animation", "Family", "Adventure"]
        )
    ]
    
    // MARK: - Popular TV Shows (Recent 2024)
    let popularTVShows: [MediaDisplayItem] = [
        MediaDisplayItem(
            id: 94997,
            title: "House of the Dragon",
            year: "2022",
            rating: 8.4,
            overview: "The Targaryen dynasty is at the absolute apex of its power, with more than 15 dragons under their yoke. Most empires crumble from such heights. In the case of the Targaryens, their slow fall begins when King Viserys breaks with a century of tradition by naming his daughter Rhaenyra heir to the Iron Throne.",
            posterURL: URL(string: "https://image.tmdb.org/t/p/w500/7QMsOTMUswlwxJP0rTTZfmz2tX2.jpg"),
            backdropURL: URL(string: "https://image.tmdb.org/t/p/w1280/etj8E2o0Bud0HkONVQPjyCkIvpv.jpg"),
            type: .tvShow,
            runtime: 60,
            genres: ["Drama", "Fantasy", "Action & Adventure"]
        ),
        MediaDisplayItem(
            id: 136315,
            title: "The Bear",
            year: "2022",
            rating: 8.7,
            overview: "Carmen 'Carmy' Berzatto, a young chef from the fine dining world, comes home to Chicago to run his deceased brother's Italian beef sandwich shop. It's a world away from what he's used to, and he must balance the soul-crushing reality of trading in sandwiches with his culinary dreams.",
            posterURL: URL(string: "https://image.tmdb.org/t/p/w500/sHFlbKS3WLqMnp9t2ghADIJFnuQ.jpg"),
            backdropURL: URL(string: "https://image.tmdb.org/t/p/w1280/zPIug5giU8oug6Xes5K1sTfQJxY.jpg"),
            type: .tvShow,
            runtime: 30,
            genres: ["Comedy", "Drama"]
        ),
        MediaDisplayItem(
            id: 119051,
            title: "Wednesday",
            year: "2022",
            rating: 8.5,
            overview: "Wednesday Addams is sent to Nevermore Academy, a bizarre boarding school where she attempts to master her psychic powers, stop a monstrous killing spree of the town citizens, and solve the supernatural mystery that affected her family 25 years ago — all while navigating her new relationships.",
            posterURL: URL(string: "https://image.tmdb.org/t/p/w500/9PFonBhy4cQy7Jz20NpMygczOkv.jpg"),
            backdropURL: URL(string: "https://image.tmdb.org/t/p/w1280/iHSwvRVsRyxpX7FE7GbviaDvgGZ.jpg"),
            type: .tvShow,
            runtime: 50,
            genres: ["Mystery", "Comedy", "Drama"]
        )
    ]
    
    // MARK: - Combined Content
    var trendingContent: [MediaDisplayItem] {
        return Array((popularMovies + popularTVShows).shuffled().prefix(6))
    }
    
    var recentlyAdded: [MediaDisplayItem] {
        return Array((popularMovies + popularTVShows).shuffled().prefix(5))
    }
    
    var topRatedMovies: [MediaDisplayItem] {
        return popularMovies.sorted { $0.rating > $1.rating }
    }
    
    var trendingShows: [MediaDisplayItem] {
        return popularTVShows.sorted { $0.rating > $1.rating }
    }
}
