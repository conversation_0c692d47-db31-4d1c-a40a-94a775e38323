import Foundation
import SwiftUI
import SwiftUICore
import UserNotifications
import CoreData

// MARK: - Notification Manager
class NotificationManager: NSObject, ObservableObject {
    static let shared = NotificationManager()
    
    @Published var isAuthorized = false
    @Published var releaseNotificationsEnabled = true
    @Published var dailyRecommendationsEnabled = true
    @Published var notificationTime = Date()
    
    private let center = UNUserNotificationCenter.current()
    private let tmdbService = TMDBService.shared
    private let recommendationEngine = RecommendationEngine.shared
    
    override init() {
        super.init()
        center.delegate = self
        loadSettings()
        checkAuthorizationStatus()
    }
    
    // MARK: - Authorization
    func requestPermission() async -> Bool {
        do {
            let granted = try await center.requestAuthorization(options: [.alert, .badge, .sound])
            await MainActor.run {
                isAuthorized = granted
            }
            return granted
        } catch {
            print("Notification permission error: \(error)")
            return false
        }
    }
    
    func checkAuthorizationStatus() {
        center.getNotificationSettings { settings in
            DispatchQueue.main.async {
                self.isAuthorized = settings.authorizationStatus == .authorized
            }
        }
    }
    
    // MARK: - Settings Management
    func updateSettings(
        releaseNotifications: Bool,
        dailyRecommendations: Bool,
        notificationTime: Date
    ) {
        self.releaseNotificationsEnabled = releaseNotifications
        self.dailyRecommendationsEnabled = dailyRecommendations
        self.notificationTime = notificationTime
        
        saveSettings()
        scheduleNotifications()
    }
    
    private func saveSettings() {
        UserDefaults.standard.set(releaseNotificationsEnabled, forKey: "releaseNotificationsEnabled")
        UserDefaults.standard.set(dailyRecommendationsEnabled, forKey: "dailyRecommendationsEnabled")
        UserDefaults.standard.set(notificationTime, forKey: "notificationTime")
    }
    
    private func loadSettings() {
        releaseNotificationsEnabled = UserDefaults.standard.bool(forKey: "releaseNotificationsEnabled")
        dailyRecommendationsEnabled = UserDefaults.standard.bool(forKey: "dailyRecommendationsEnabled")
        
        if let savedTime = UserDefaults.standard.object(forKey: "notificationTime") as? Date {
            notificationTime = savedTime
        } else {
            // Default to 7 PM
            let calendar = Calendar.current
            notificationTime = calendar.date(bySettingHour: 19, minute: 0, second: 0, of: Date()) ?? Date()
        }
    }
    
    // MARK: - Notification Scheduling
    func scheduleNotifications() {
        // Remove all pending notifications
        center.removeAllPendingNotificationRequests()
        
        guard isAuthorized else { return }
        
        // Schedule daily recommendations
        if dailyRecommendationsEnabled {
            scheduleDailyRecommendations()
        }
        
        // Schedule release notifications for watchlist items
        if releaseNotificationsEnabled {
            scheduleReleaseNotifications()
        }
    }
    
    private func scheduleDailyRecommendations() {
        let content = UNMutableNotificationContent()
        content.title = "Daily Recommendations"
        content.body = "Discover new movies and TV shows picked just for you!"
        content.sound = .default
        content.categoryIdentifier = "DAILY_RECOMMENDATIONS"
        
        let calendar = Calendar.current
        let components = calendar.dateComponents([.hour, .minute], from: notificationTime)
        
        let trigger = UNCalendarNotificationTrigger(dateMatching: components, repeats: true)
        
        let request = UNNotificationRequest(
            identifier: "daily_recommendations",
            content: content,
            trigger: trigger
        )
        
        center.add(request) { error in
            if let error = error {
                print("Error scheduling daily recommendations: \(error)")
            }
        }
    }
    
    private func scheduleReleaseNotifications() {
        // This would typically fetch watchlist items and check for upcoming releases
        // For now, we'll implement a basic version
        Task {
            await scheduleWatchlistReleaseNotifications()
        }
    }
    
    private func scheduleWatchlistReleaseNotifications() async {
        // TODO: Implement watchlist release notifications
        // This would involve:
        // 1. Fetching user's watchlist from Core Data
        // 2. Checking TMDB for release dates
        // 3. Scheduling notifications for upcoming releases
        print("Scheduling watchlist release notifications...")
    }
    
    // MARK: - Immediate Notifications
    func sendDailyRecommendationNotification(recommendations: [RecommendationItem]) {
        guard isAuthorized && dailyRecommendationsEnabled else { return }
        
        let content = UNMutableNotificationContent()
        content.title = "Your Daily Picks"
        
        if let firstRec = recommendations.first {
            content.body = "Check out \(firstRec.title) and \(recommendations.count - 1) other recommendations!"
        } else {
            content.body = "New personalized recommendations are ready!"
        }
        
        content.sound = .default
        content.categoryIdentifier = "DAILY_RECOMMENDATIONS"
        content.userInfo = ["type": "daily_recommendations"]
        
        let request = UNNotificationRequest(
            identifier: "daily_recommendations_\(Date().timeIntervalSince1970)",
            content: content,
            trigger: nil // Send immediately
        )
        
        center.add(request) { error in
            if let error = error {
                print("Error sending daily recommendation notification: \(error)")
            }
        }
    }
    
    func sendReleaseNotification(for title: String, type: String) {
        guard isAuthorized && releaseNotificationsEnabled else { return }
        
        let content = UNMutableNotificationContent()
        content.title = "New Release!"
        content.body = "\(title) is now available to watch!"
        content.sound = .default
        content.categoryIdentifier = "RELEASE_NOTIFICATION"
        content.userInfo = ["type": "release", "title": title, "mediaType": type]
        
        let request = UNNotificationRequest(
            identifier: "release_\(title.replacingOccurrences(of: " ", with: "_"))",
            content: content,
            trigger: nil
        )
        
        center.add(request) { error in
            if let error = error {
                print("Error sending release notification: \(error)")
            }
        }
    }
    
    // MARK: - Notification Actions
    func setupNotificationCategories() {
        // Daily Recommendations Actions
        let viewRecommendations = UNNotificationAction(
            identifier: "VIEW_RECOMMENDATIONS",
            title: "View Recommendations",
            options: [.foreground]
        )
        
        let dismissRecommendations = UNNotificationAction(
            identifier: "DISMISS_RECOMMENDATIONS",
            title: "Dismiss",
            options: []
        )
        
        let recommendationsCategory = UNNotificationCategory(
            identifier: "DAILY_RECOMMENDATIONS",
            actions: [viewRecommendations, dismissRecommendations],
            intentIdentifiers: [],
            options: []
        )
        
        // Release Notification Actions
        let addToWatchlist = UNNotificationAction(
            identifier: "ADD_TO_WATCHLIST",
            title: "Add to Watchlist",
            options: []
        )
        
        let viewDetails = UNNotificationAction(
            identifier: "VIEW_DETAILS",
            title: "View Details",
            options: [.foreground]
        )
        
        let releaseCategory = UNNotificationCategory(
            identifier: "RELEASE_NOTIFICATION",
            actions: [addToWatchlist, viewDetails],
            intentIdentifiers: [],
            options: []
        )
        
        center.setNotificationCategories([recommendationsCategory, releaseCategory])
    }
}

// MARK: - UNUserNotificationCenterDelegate
extension NotificationManager: UNUserNotificationCenterDelegate {
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
    ) {
        // Show notification even when app is in foreground
        completionHandler([.banner, .sound, .badge])
    }
    
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        didReceive response: UNNotificationResponse,
        withCompletionHandler completionHandler: @escaping () -> Void
    ) {
        let userInfo = response.notification.request.content.userInfo
        
        switch response.actionIdentifier {
        case "VIEW_RECOMMENDATIONS":
            // Navigate to AI recommendations view
            NotificationCenter.default.post(name: .navigateToRecommendations, object: nil)
            
        case "ADD_TO_WATCHLIST":
            if let title = userInfo["title"] as? String {
                // Add to watchlist
                print("Adding \(title) to watchlist from notification")
            }
            
        case "VIEW_DETAILS":
            if let title = userInfo["title"] as? String {
                // Navigate to details view
                NotificationCenter.default.post(
                    name: .navigateToDetails,
                    object: nil,
                    userInfo: ["title": title]
                )
            }
            
        case UNNotificationDefaultActionIdentifier:
            // User tapped the notification
            if let type = userInfo["type"] as? String {
                switch type {
                case "daily_recommendations":
                    NotificationCenter.default.post(name: .navigateToRecommendations, object: nil)
                case "release":
                    if let title = userInfo["title"] as? String {
                        NotificationCenter.default.post(
                            name: .navigateToDetails,
                            object: nil,
                            userInfo: ["title": title]
                        )
                    }
                default:
                    break
                }
            }
            
        default:
            break
        }
        
        completionHandler()
    }
}

// MARK: - Notification Names
extension Notification.Name {
    static let navigateToRecommendations = Notification.Name("navigateToRecommendations")
    static let navigateToDetails = Notification.Name("navigateToDetails")
}

// MARK: - Notification Settings View
struct NotificationSettingsView: View {
    @StateObject private var notificationManager = NotificationManager.shared
    @State private var showingPermissionAlert = false
    
    var body: some View {
        NavigationView {
            List {
                // Permission Status
                Section("Notification Permission") {
                    HStack {
                        Image(systemName: notificationManager.isAuthorized ? "checkmark.circle.fill" : "xmark.circle.fill")
                            .foregroundColor(notificationManager.isAuthorized ? .green : .red)
                        
                        VStack(alignment: .leading) {
                            Text("Notifications")
                                .font(.headline)
                            Text(notificationManager.isAuthorized ? "Enabled" : "Disabled")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        if !notificationManager.isAuthorized {
                            Button("Enable") {
                                Task {
                                    await notificationManager.requestPermission()
                                }
                            }
                        }
                    }
                }
                
                if notificationManager.isAuthorized {
                    // Notification Types
                    Section("Notification Types") {
                        Toggle("Release Notifications", isOn: $notificationManager.releaseNotificationsEnabled)
                        Toggle("Daily Recommendations", isOn: $notificationManager.dailyRecommendationsEnabled)
                    }
                    
                    // Timing
                    Section("Daily Recommendation Time") {
                        DatePicker(
                            "Notification Time",
                            selection: $notificationManager.notificationTime,
                            displayedComponents: .hourAndMinute
                        )
                    }
                    
                    // Save Button
                    Section {
                        Button("Save Settings") {
                            notificationManager.updateSettings(
                                releaseNotifications: notificationManager.releaseNotificationsEnabled,
                                dailyRecommendations: notificationManager.dailyRecommendationsEnabled,
                                notificationTime: notificationManager.notificationTime
                            )
                        }
                        .frame(maxWidth: .infinity)
                    }
                }
            }
            .navigationTitle("Notifications")
            .onAppear {
                notificationManager.checkAuthorizationStatus()
                notificationManager.setupNotificationCategories()
            }
        }
    }
}

#Preview {
    NotificationSettingsView()
}
