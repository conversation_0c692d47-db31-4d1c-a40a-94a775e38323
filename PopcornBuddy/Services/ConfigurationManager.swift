import Foundation
import SwiftUI
import SwiftUI<PERSON>ore
import Security

// MARK: - Configuration Manager
class ConfigurationManager {
    static let shared = ConfigurationManager()
    
    private let keychain = KeychainManager()
    private var environmentConfig: [String: String] = [:]
    
    private init() {
        loadEnvironmentConfiguration()
    }
    
    // MARK: - API Keys
    var tmdbAPIKey: String {
        return getSecureValue(for: "TMDB_API_KEY") ?? "YOUR_TMDB_API_KEY"
    }
    
    var openAIAPIKey: String {
        return getSecureValue(for: "OPENAI_API_KEY") ?? "YOUR_OPENAI_API_KEY"
    }
    
    // MARK: - OpenAI Configuration
    var openAIModel: String {
        return environmentConfig["OPENAI_MODEL"] ?? "gpt-4o-mini"
    }
    
    var openAIMaxTokens: Int {
        return Int(environmentConfig["OPENAI_MAX_TOKENS"] ?? "1500") ?? 1500
    }
    
    var openAITemperature: Double {
        return Double(environmentConfig["OPENAI_TEMPERATURE"] ?? "0.7") ?? 0.7
    }
    
    // MARK: - Rate Limiting
    var maxDailyAIRequests: Int {
        return Int(environmentConfig["MAX_DAILY_AI_REQUESTS"] ?? "50") ?? 50
    }
    
    var cacheDurationHours: Int {
        return Int(environmentConfig["CACHE_DURATION_HOURS"] ?? "24") ?? 24
    }
    
    // MARK: - Notification Configuration
    var notificationsEnabled: Bool {
        return Bool(environmentConfig["ENABLE_NOTIFICATIONS"] ?? "true") ?? true
    }
    
    var notificationHour: Int {
        return Int(environmentConfig["NOTIFICATION_TIME_HOUR"] ?? "19") ?? 19
    }
    
    var notificationMinute: Int {
        return Int(environmentConfig["NOTIFICATION_TIME_MINUTE"] ?? "0") ?? 0
    }
    
    // MARK: - Setup Methods
    func setupAPIKeys(tmdbKey: String, openAIKey: String) {
        keychain.store(key: "TMDB_API_KEY", value: tmdbKey)
        keychain.store(key: "OPENAI_API_KEY", value: openAIKey)
    }
    
    func hasValidAPIKeys() -> Bool {
        let tmdbKey = tmdbAPIKey
        let openAIKey = openAIAPIKey
        
        return !tmdbKey.contains("YOUR_") && !openAIKey.contains("YOUR_") &&
               !tmdbKey.isEmpty && !openAIKey.isEmpty
    }
    
    // MARK: - Private Methods
    private func loadEnvironmentConfiguration() {
        guard let path = Bundle.main.path(forResource: ".local", ofType: "env") else {
            print("⚠️ .local.env file not found. Using default configuration.")
            return
        }
        
        do {
            let content = try String(contentsOfFile: path)
            parseEnvironmentFile(content)
        } catch {
            print("⚠️ Error reading .local.env file: \(error)")
        }
    }
    
    private func parseEnvironmentFile(_ content: String) {
        let lines = content.components(separatedBy: .newlines)
        
        for line in lines {
            let trimmed = line.trimmingCharacters(in: .whitespaces)
            
            // Skip comments and empty lines
            if trimmed.isEmpty || trimmed.hasPrefix("#") {
                continue
            }
            
            // Parse key=value pairs
            let components = trimmed.components(separatedBy: "=")
            if components.count == 2 {
                let key = components[0].trimmingCharacters(in: .whitespaces)
                let value = components[1].trimmingCharacters(in: .whitespaces)
                
                // Store API keys securely
                if key.contains("API_KEY") {
                    keychain.store(key: key, value: value)
                } else {
                    environmentConfig[key] = value
                }
            }
        }
    }
    
    private func getSecureValue(for key: String) -> String? {
        return keychain.retrieve(key: key)
    }
}

// MARK: - Keychain Manager
class KeychainManager {
    private let service = "bingqi.PopcornBuddy.keys"
    
    func store(key: String, value: String) {
        let data = value.data(using: .utf8)!
        
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key,
            kSecValueData as String: data
        ]
        
        // Delete existing item
        SecItemDelete(query as CFDictionary)
        
        // Add new item
        let status = SecItemAdd(query as CFDictionary, nil)
        
        if status != errSecSuccess {
            print("⚠️ Failed to store \(key) in keychain: \(status)")
        }
    }
    
    func retrieve(key: String) -> String? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        if status == errSecSuccess,
           let data = result as? Data,
           let string = String(data: data, encoding: .utf8) {
            return string
        }
        
        return nil
    }
    
    func delete(key: String) {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key
        ]
        
        SecItemDelete(query as CFDictionary)
    }
}

// MARK: - API Key Setup View
struct APIKeySetupView: View {
    @State private var tmdbKey = ""
    @State private var openAIKey = ""
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // Header
                VStack(spacing: 8) {
                    Image(systemName: "key.fill")
                        .font(.system(size: 50))
                        .foregroundColor(.blue)
                    
                    Text("API Configuration")
                        .font(.title)
                        .fontWeight(.bold)
                    
                    Text("Enter your API keys to enable all features")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                
                // API Key Forms
                VStack(spacing: 16) {
                    // TMDB API Key
                    VStack(alignment: .leading, spacing: 8) {
                        Text("TMDB API Key")
                            .font(.headline)
                        
                        SecureField("Enter TMDB API key", text: $tmdbKey)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                        
                        Text("Get your free API key from themoviedb.org")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    // OpenAI API Key
                    VStack(alignment: .leading, spacing: 8) {
                        Text("OpenAI API Key")
                            .font(.headline)
                        
                        SecureField("Enter OpenAI API key", text: $openAIKey)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                        
                        Text("Get your API key from platform.openai.com (requires billing)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                // Save Button
                Button(action: saveAPIKeys) {
                    Text("Save Configuration")
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.blue)
                        )
                }
                .disabled(tmdbKey.isEmpty || openAIKey.isEmpty)
                
                Spacer()
            }
            .padding()
            .navigationTitle("Setup")
            .navigationBarTitleDisplayMode(.inline)
            .alert("Configuration", isPresented: $showingAlert) {
                Button("OK") {
                    if alertMessage.contains("Success") {
                        dismiss()
                    }
                }
            } message: {
                Text(alertMessage)
            }
        }
    }
    
    private func saveAPIKeys() {
        guard !tmdbKey.isEmpty && !openAIKey.isEmpty else {
            alertMessage = "Please enter both API keys"
            showingAlert = true
            return
        }
        
        ConfigurationManager.shared.setupAPIKeys(tmdbKey: tmdbKey, openAIKey: openAIKey)
        
        alertMessage = "API keys saved successfully! The app will now have full functionality."
        showingAlert = true
    }
}

#Preview {
    APIKeySetupView()
}
