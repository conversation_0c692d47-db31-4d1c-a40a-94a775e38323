import Foundation
import CoreData
import Combine

// MARK: - Daily Recommendation Manager
class DailyRecommendationManager: ObservableObject {
    static let shared = DailyRecommendationManager()
    
    @Published var todaysMovieRecommendation: DailyRecommendation?
    @Published var todaysTVShowRecommendation: DailyRecommendation?
    @Published var isLoading = false
    @Published var hasSeenTodaysRecommendations = false
    
    private let openAIService = OpenAIService.shared
    private let tmdbService = TMDBService.shared
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        checkTodaysRecommendations()
    }
    
    // MARK: - Daily Recommendation Generation
    func generateDailyRecommendations(for context: NSManagedObjectContext) async {
        let today = Calendar.current.startOfDay(for: Date())
        let lastGeneratedKey = "lastDailyRecommendationDate"
        
        // Check if we already generated recommendations today
        if let lastGenerated = UserDefaults.standard.object(forKey: lastGeneratedKey) as? Date,
           Calendar.current.isDate(lastGenerated, inSameDayAs: today) {
            loadTodaysRecommendations()
            return
        }
        
        await MainActor.run {
            isLoading = true
        }
        
        do {
            // Get user preferences
            let userProfile = await buildUserProfile(context: context)
            
            // Generate movie recommendation
            let movieRec = try await generateDailyMovieRecommendation(userProfile: userProfile)
            
            // Generate TV show recommendation
            let tvShowRec = try await generateDailyTVShowRecommendation(userProfile: userProfile)
            
            await MainActor.run {
                self.todaysMovieRecommendation = movieRec
                self.todaysTVShowRecommendation = tvShowRec
                self.hasSeenTodaysRecommendations = false
                self.isLoading = false
            }
            
            // Save to UserDefaults
            saveTodaysRecommendations()
            UserDefaults.standard.set(Date(), forKey: lastGeneratedKey)
            
        } catch {
            print("Error generating daily recommendations: \(error)")
            await MainActor.run {
                self.isLoading = false
            }
        }
    }
    
    // MARK: - User Feedback
    func provideFeedback(
        for recommendation: DailyRecommendation,
        feedback: RecommendationFeedback
    ) {
        // Update recommendation with feedback
        recommendation.userFeedback = feedback
        recommendation.feedbackDate = Date()
        
        // Save feedback for future AI improvements
        saveFeedbackForAI(recommendation: recommendation, feedback: feedback)
        
        // Update UI
        objectWillChange.send()
        
        // Save to UserDefaults
        saveTodaysRecommendations()
    }
    
    // MARK: - Preference Management
    func updateRecommendationPreference(_ preference: RecommendationPreference) {
        UserDefaults.standard.set(preference.rawValue, forKey: "dailyRecommendationPreference")
    }
    
    func getRecommendationPreference() -> RecommendationPreference {
        let rawValue = UserDefaults.standard.string(forKey: "dailyRecommendationPreference") ?? RecommendationPreference.balanced.rawValue
        return RecommendationPreference(rawValue: rawValue) ?? .balanced
    }
    
    // MARK: - Private Methods
    private func sendDailyRecommendationRequest(_ request: OpenAIChatRequest) async throws -> OpenAIChatResponse {
        guard let url = URL(string: "https://api.openai.com/v1/chat/completions") else {
            throw DailyRecommendationError.invalidResponse
        }

        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("Bearer \(ConfigurationManager.shared.openAIAPIKey)", forHTTPHeaderField: "Authorization")
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")

        let encoder = JSONEncoder()
        urlRequest.httpBody = try encoder.encode(request)

        let (data, response) = try await URLSession.shared.data(for: urlRequest)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw DailyRecommendationError.invalidResponse
        }

        guard httpResponse.statusCode == 200 else {
            throw DailyRecommendationError.invalidResponse
        }

        let decoder = JSONDecoder()
        return try decoder.decode(OpenAIChatResponse.self, from: data)
    }

    private func buildUserProfile(context: NSManagedObjectContext) async -> DailyUserProfile {
        return await withCheckedContinuation { continuation in
            context.perform {
                // Fetch user's watch history
                let movieRequest: NSFetchRequest<Movie> = Movie.fetchRequest()
                movieRequest.predicate = NSPredicate(format: "isWatched == YES")
                movieRequest.sortDescriptors = [NSSortDescriptor(key: "watchedDate", ascending: false)]
                movieRequest.fetchLimit = 20
                
                let tvShowRequest: NSFetchRequest<TVShow> = TVShow.fetchRequest()
                tvShowRequest.predicate = NSPredicate(format: "isWatched == YES")
                tvShowRequest.sortDescriptors = [NSSortDescriptor(key: "userRating", ascending: false)]
                tvShowRequest.fetchLimit = 20
                
                do {
                    let recentMovies = try context.fetch(movieRequest)
                    let topTVShows = try context.fetch(tvShowRequest)
                    
                    // Get feedback history
                    let feedbackHistory = self.getFeedbackHistory()
                    
                    let profile = DailyUserProfile(
                        recentMovies: recentMovies,
                        topTVShows: topTVShows,
                        feedbackHistory: feedbackHistory,
                        preference: self.getRecommendationPreference()
                    )
                    
                    continuation.resume(returning: profile)
                } catch {
                    print("Error building user profile: \(error)")
                    continuation.resume(returning: DailyUserProfile(
                        recentMovies: [],
                        topTVShows: [],
                        feedbackHistory: [],
                        preference: .balanced
                    ))
                }
            }
        }
    }
    
    private func generateDailyMovieRecommendation(userProfile: DailyUserProfile) async throws -> DailyRecommendation {
        let prompt = buildMovieRecommendationPrompt(userProfile: userProfile)
        
        let request = OpenAIChatRequest(
            model: ConfigurationManager.shared.openAIModel,
            messages: [
                OpenAIChatMessage(role: "system", content: dailyRecommendationSystemPrompt),
                OpenAIChatMessage(role: "user", content: prompt)
            ],
            maxTokens: 800, temperature: ConfigurationManager.shared.openAITemperature
        )
        
        let response = try await sendDailyRecommendationRequest(request)
        let content = response.choices.first?.message.content ?? ""

        // Parse the AI response to extract recommendation details
        return try parseDailyRecommendation(from: content, type: .movie)
    }
    
    private func generateDailyTVShowRecommendation(userProfile: DailyUserProfile) async throws -> DailyRecommendation {
        let prompt = buildTVShowRecommendationPrompt(userProfile: userProfile)
        
        let request = OpenAIChatRequest(
            model: ConfigurationManager.shared.openAIModel,
            messages: [
                OpenAIChatMessage(role: "system", content: dailyRecommendationSystemPrompt),
                OpenAIChatMessage(role: "user", content: prompt)
            ],
            maxTokens: 800, temperature: ConfigurationManager.shared.openAITemperature
        )
        
        let response = try await sendDailyRecommendationRequest(request)
        let content = response.choices.first?.message.content ?? ""

        return try parseDailyRecommendation(from: content, type: .tvShow)
    }
    
    private func buildMovieRecommendationPrompt(userProfile: DailyUserProfile) -> String {
        var prompt = """
        Generate ONE daily movie recommendation for this user.
        
        User's Recent Movies (last 20 watched):
        """
        
        for movie in userProfile.recentMovies.prefix(10) {
            prompt += "\n- \(movie.title ?? "Unknown") (Rating: \(movie.userRating)/10)"
        }
        
        prompt += """
        
        User Preference: \(userProfile.preference.description)
        
        Previous Feedback:
        """
        
        for feedback in userProfile.feedbackHistory.suffix(5) {
            prompt += "\n- \(feedback.title): \(feedback.feedback.description)"
        }
        
        prompt += """
        
        Please recommend ONE movie that:
        1. Matches their preference type (\(userProfile.preference.description))
        2. Considers their feedback history
        3. Is different from what they've recently watched
        
        Format as JSON:
        {
            "title": "Movie Title",
            "year": "2024",
            "reason": "Why this movie is perfect for them today",
            "confidence": 0.85,
            "tmdb_id": 12345,
            "genre": "Primary Genre"
        }
        """
        
        return prompt
    }
    
    private func buildTVShowRecommendationPrompt(userProfile: DailyUserProfile) -> String {
        var prompt = """
        Generate ONE daily TV show recommendation for this user.
        
        User's Top TV Shows:
        """
        
        for show in userProfile.topTVShows.prefix(10) {
            prompt += "\n- \(show.name ?? "Unknown") (Rating: \(show.userRating)/10)"
        }
        
        prompt += """
        
        User Preference: \(userProfile.preference.description)
        
        Previous Feedback:
        """
        
        for feedback in userProfile.feedbackHistory.suffix(5) {
            prompt += "\n- \(feedback.title): \(feedback.feedback.description)"
        }
        
        prompt += """
        
        Please recommend ONE TV show that:
        1. Matches their preference type (\(userProfile.preference.description))
        2. Considers their feedback history
        3. Offers something fresh based on their viewing patterns
        
        Format as JSON:
        {
            "title": "TV Show Title",
            "year": "2024",
            "reason": "Why this show is perfect for them today",
            "confidence": 0.85,
            "tmdb_id": 12345,
            "genre": "Primary Genre"
        }
        """
        
        return prompt
    }
    
    private var dailyRecommendationSystemPrompt: String {
        """
        You are a personalized daily recommendation system for movies and TV shows. Your goal is to provide exactly ONE high-quality recommendation per day that perfectly matches the user's current mood and preferences.
        
        Key principles:
        1. Quality over quantity - ONE perfect recommendation is better than many good ones
        2. Consider user feedback to improve future recommendations
        3. Balance familiarity with discovery based on user preference
        4. Provide compelling reasons that make the user excited to watch
        5. Be confident in your recommendations
        
        Always respond with valid JSON format.
        """
    }
    
    private func parseDailyRecommendation(from content: String, type: RecommendationType) throws -> DailyRecommendation {
        // Extract JSON from the response
        guard let jsonData = content.data(using: .utf8) else {
            throw DailyRecommendationError.invalidResponse
        }
        
        let decoder = JSONDecoder()
        let parsed = try decoder.decode(ParsedDailyRecommendation.self, from: jsonData)
        
        return DailyRecommendation(
            id: UUID(),
            title: parsed.title,
            year: parsed.year,
            reason: parsed.reason,
            confidence: parsed.confidence,
            tmdbID: parsed.tmdb_id,
            genre: parsed.genre,
            type: type,
            generatedDate: Date(),
            userFeedback: nil,
            feedbackDate: nil
        )
    }
    
    private func saveFeedbackForAI(recommendation: DailyRecommendation, feedback: RecommendationFeedback) {
        let feedbackItem = FeedbackHistoryItem(
            title: recommendation.title,
            type: recommendation.type,
            feedback: feedback,
            date: Date(),
            reason: recommendation.reason
        )
        
        var history = getFeedbackHistory()
        history.append(feedbackItem)
        
        // Keep only last 50 feedback items
        if history.count > 50 {
            history = Array(history.suffix(50))
        }
        
        if let encoded = try? JSONEncoder().encode(history) {
            UserDefaults.standard.set(encoded, forKey: "feedbackHistory")
        }
    }
    
    private func getFeedbackHistory() -> [FeedbackHistoryItem] {
        guard let data = UserDefaults.standard.data(forKey: "feedbackHistory"),
              let history = try? JSONDecoder().decode([FeedbackHistoryItem].self, from: data) else {
            return []
        }
        return history
    }
    
    private func checkTodaysRecommendations() {
        loadTodaysRecommendations()
        
        let hasSeenKey = "hasSeenTodaysRecommendations_\(Calendar.current.startOfDay(for: Date()).timeIntervalSince1970)"
        hasSeenTodaysRecommendations = UserDefaults.standard.bool(forKey: hasSeenKey)
    }
    
    private func loadTodaysRecommendations() {
        let today = Calendar.current.startOfDay(for: Date())
        let movieKey = "todaysMovieRecommendation_\(today.timeIntervalSince1970)"
        let tvShowKey = "todaysTVShowRecommendation_\(today.timeIntervalSince1970)"
        
        if let movieData = UserDefaults.standard.data(forKey: movieKey),
           let movieRec = try? JSONDecoder().decode(DailyRecommendation.self, from: movieData) {
            todaysMovieRecommendation = movieRec
        }
        
        if let tvShowData = UserDefaults.standard.data(forKey: tvShowKey),
           let tvShowRec = try? JSONDecoder().decode(DailyRecommendation.self, from: tvShowData) {
            todaysTVShowRecommendation = tvShowRec
        }
    }
    
    private func saveTodaysRecommendations() {
        let today = Calendar.current.startOfDay(for: Date())
        let movieKey = "todaysMovieRecommendation_\(today.timeIntervalSince1970)"
        let tvShowKey = "todaysTVShowRecommendation_\(today.timeIntervalSince1970)"
        
        if let movieRec = todaysMovieRecommendation,
           let movieData = try? JSONEncoder().encode(movieRec) {
            UserDefaults.standard.set(movieData, forKey: movieKey)
        }
        
        if let tvShowRec = todaysTVShowRecommendation,
           let tvShowData = try? JSONEncoder().encode(tvShowRec) {
            UserDefaults.standard.set(tvShowData, forKey: tvShowKey)
        }
    }
    
    func markRecommendationsAsSeen() {
        let hasSeenKey = "hasSeenTodaysRecommendations_\(Calendar.current.startOfDay(for: Date()).timeIntervalSince1970)"
        UserDefaults.standard.set(true, forKey: hasSeenKey)
        hasSeenTodaysRecommendations = true
    }
}

// MARK: - Supporting Types
struct DailyUserProfile {
    let recentMovies: [Movie]
    let topTVShows: [TVShow]
    let feedbackHistory: [FeedbackHistoryItem]
    let preference: RecommendationPreference
}

class DailyRecommendation: ObservableObject, Codable, Identifiable {
    let id: UUID
    let title: String
    let year: String
    let reason: String
    let confidence: Double
    let tmdbID: Int
    let genre: String
    let type: RecommendationType
    let generatedDate: Date
    
    @Published var userFeedback: RecommendationFeedback?
    @Published var feedbackDate: Date?
    
    init(id: UUID, title: String, year: String, reason: String, confidence: Double, tmdbID: Int, genre: String, type: RecommendationType, generatedDate: Date, userFeedback: RecommendationFeedback?, feedbackDate: Date?) {
        self.id = id
        self.title = title
        self.year = year
        self.reason = reason
        self.confidence = confidence
        self.tmdbID = tmdbID
        self.genre = genre
        self.type = type
        self.generatedDate = generatedDate
        self.userFeedback = userFeedback
        self.feedbackDate = feedbackDate
    }
    
    // Codable implementation
    enum CodingKeys: String, CodingKey {
        case id, title, year, reason, confidence, tmdbID, genre, type, generatedDate, userFeedback, feedbackDate
    }
    
    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        title = try container.decode(String.self, forKey: .title)
        year = try container.decode(String.self, forKey: .year)
        reason = try container.decode(String.self, forKey: .reason)
        confidence = try container.decode(Double.self, forKey: .confidence)
        tmdbID = try container.decode(Int.self, forKey: .tmdbID)
        genre = try container.decode(String.self, forKey: .genre)
        type = try container.decode(RecommendationType.self, forKey: .type)
        generatedDate = try container.decode(Date.self, forKey: .generatedDate)
        userFeedback = try container.decodeIfPresent(RecommendationFeedback.self, forKey: .userFeedback)
        feedbackDate = try container.decodeIfPresent(Date.self, forKey: .feedbackDate)
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(title, forKey: .title)
        try container.encode(year, forKey: .year)
        try container.encode(reason, forKey: .reason)
        try container.encode(confidence, forKey: .confidence)
        try container.encode(tmdbID, forKey: .tmdbID)
        try container.encode(genre, forKey: .genre)
        try container.encode(type, forKey: .type)
        try container.encode(generatedDate, forKey: .generatedDate)
        try container.encodeIfPresent(userFeedback, forKey: .userFeedback)
        try container.encodeIfPresent(feedbackDate, forKey: .feedbackDate)
    }
}

struct ParsedDailyRecommendation: Codable {
    let title: String
    let year: String
    let reason: String
    let confidence: Double
    let tmdb_id: Int
    let genre: String
}

enum RecommendationFeedback: String, Codable, CaseIterable {
    case willWatch = "will_watch"
    case maybeNo = "maybe_no"
    
    var description: String {
        switch self {
        case .willWatch:
            return "I'll watch it"
        case .maybeNo:
            return "Maybe no"
        }
    }
}

enum RecommendationPreference: String, Codable, CaseIterable {
    case familiar = "familiar"
    case discovery = "discovery"
    case balanced = "balanced"
    
    var description: String {
        switch self {
        case .familiar:
            return "Familiar genres and styles I already love"
        case .discovery:
            return "New genres and experiences to explore"
        case .balanced:
            return "Mix of familiar and new content"
        }
    }
}

struct FeedbackHistoryItem: Codable {
    let title: String
    let type: RecommendationType
    let feedback: RecommendationFeedback
    let date: Date
    let reason: String
}

enum DailyRecommendationError: Error {
    case invalidResponse
    case noRecommendations
    case alreadyGenerated
}
