import SwiftUI

struct DailyRecommendationsView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @StateObject private var dailyManager = DailyRecommendationManager.shared
    @State private var showingPreferences = false
    @State private var selectedRecommendation: DailyRecommendation?
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    headerView
                    
                    // Daily Recommendations
                    if dailyManager.isLoading {
                        loadingView
                    } else if dailyManager.todaysMovieRecommendation == nil && dailyManager.todaysTVShowRecommendation == nil {
                        emptyStateView
                    } else {
                        recommendationsView
                    }
                    
                    // Preferences Section
                    preferencesSection
                    
                    Spacer(minLength: 100)
                }
                .padding()
            }
            .navigationTitle("Daily Picks")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { showingPreferences = true }) {
                        Image(systemName: "slider.horizontal.3")
                    }
                }
            }
            .refreshable {
                await generateNewRecommendations()
            }
            .onAppear {
                if dailyManager.todaysMovieRecommendation == nil {
                    Task {
                        await dailyManager.generateDailyRecommendations(for: viewContext)
                    }
                }
                dailyManager.markRecommendationsAsSeen()
            }
            .sheet(isPresented: $showingPreferences) {
                DailyRecommendationPreferencesView()
            }
            .sheet(item: $selectedRecommendation) { recommendation in
                DailyRecommendationDetailView(recommendation: recommendation)
            }
        }
    }
    
    // MARK: - Header View
    private var headerView: some View {
        VStack(spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Today's Picks")
                        .font(.title)
                        .fontWeight(.bold)
                    
                    Text("Curated just for you • \(Date().formatted(date: .abbreviated, time: .omitted))")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: "brain.head.profile")
                    .font(.title)
                    .foregroundColor(.blue)
                    .bounceAnimation()
            }
            
            if !dailyManager.hasSeenTodaysRecommendations && (dailyManager.todaysMovieRecommendation != nil || dailyManager.todaysTVShowRecommendation != nil) {
                HStack {
                    Image(systemName: "sparkles")
                        .foregroundColor(.yellow)
                    
                    Text("New recommendations available!")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    Spacer()
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.yellow.opacity(0.1))
                )
            }
        }
    }
    
    // MARK: - Loading View
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text("AI is selecting your perfect picks...")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(height: 200)
    }
    
    // MARK: - Empty State View
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "brain.head.profile")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text("No Recommendations Yet")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("Watch and rate some content to get personalized daily recommendations!")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button("Generate Recommendations") {
                Task {
                    await generateNewRecommendations()
                }
            }
            .buttonStyle(.borderedProminent)
        }
        .padding()
    }
    
    // MARK: - Recommendations View
    private var recommendationsView: some View {
        VStack(spacing: 20) {
            // Movie Recommendation
            if let movieRec = dailyManager.todaysMovieRecommendation {
                DailyRecommendationCard(
                    recommendation: movieRec,
                    onFeedback: { feedback in
                        dailyManager.provideFeedback(for: movieRec, feedback: feedback)
                    },
                    onViewDetails: {
                        selectedRecommendation = movieRec
                    }
                )
            }
            
            // TV Show Recommendation
            if let tvShowRec = dailyManager.todaysTVShowRecommendation {
                DailyRecommendationCard(
                    recommendation: tvShowRec,
                    onFeedback: { feedback in
                        dailyManager.provideFeedback(for: tvShowRec, feedback: feedback)
                    },
                    onViewDetails: {
                        selectedRecommendation = tvShowRec
                    }
                )
            }
        }
    }
    
    // MARK: - Preferences Section
    private var preferencesSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Recommendation Style")
                .font(.headline)
                .fontWeight(.semibold)
            
            let currentPreference = dailyManager.getRecommendationPreference()
            
            VStack(spacing: 8) {
                ForEach(RecommendationPreference.allCases, id: \.self) { preference in
                    PreferenceRow(
                        preference: preference,
                        isSelected: preference == currentPreference,
                        onSelect: {
                            dailyManager.updateRecommendationPreference(preference)
                        }
                    )
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
    
    // MARK: - Helper Methods
    private func generateNewRecommendations() async {
        await dailyManager.generateDailyRecommendations(for: viewContext)
    }
}

// MARK: - Daily Recommendation Card
struct DailyRecommendationCard: View {
    @ObservedObject var recommendation: DailyRecommendation
    let onFeedback: (RecommendationFeedback) -> Void
    let onViewDetails: () -> Void
    
    @State private var showingFeedback = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(recommendation.type == .movie ? "Today's Movie" : "Today's TV Show")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.blue)
                    
                    Text(recommendation.title)
                        .font(.title2)
                        .fontWeight(.bold)
                        .lineLimit(2)
                    
                    Text(recommendation.year)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // Confidence Badge
                VStack {
                    Text("\(Int(recommendation.confidence * 100))%")
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    Text("match")
                        .font(.caption2)
                        .foregroundColor(.white.opacity(0.8))
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(confidenceColor)
                )
            }
            
            // Reason
            Text(recommendation.reason)
                .font(.body)
                .foregroundColor(.primary)
                .lineLimit(nil)
            
            // Genre
            HStack {
                Text(recommendation.genre)
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.blue.opacity(0.1))
                    )
                    .foregroundColor(.blue)
                
                Spacer()
            }
            
            // Feedback Section
            if let feedback = recommendation.userFeedback {
                feedbackDisplayView(feedback: feedback)
            } else {
                feedbackButtonsView
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    
    private var confidenceColor: Color {
        switch recommendation.confidence {
        case 0.8...1.0:
            return .green
        case 0.6..<0.8:
            return .orange
        default:
            return .red
        }
    }
    
    private var feedbackButtonsView: some View {
        HStack(spacing: 12) {
            Button(action: {
                onFeedback(.willWatch)
            }) {
                HStack {
                    Image(systemName: "hand.thumbsup.fill")
                    Text("I'll watch it")
                        .fontWeight(.medium)
                }
                .foregroundColor(.white)
                .padding(.horizontal, 16)
                .padding(.vertical, 10)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(Color.green)
                )
            }
            
            Button(action: {
                onFeedback(.maybeNo)
            }) {
                HStack {
                    Image(systemName: "hand.thumbsdown")
                    Text("Maybe no")
                        .fontWeight(.medium)
                }
                .foregroundColor(.primary)
                .padding(.horizontal, 16)
                .padding(.vertical, 10)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(Color.gray, lineWidth: 1)
                )
            }
            
            Spacer()
            
            Button("Details") {
                onViewDetails()
            }
            .foregroundColor(.blue)
        }
    }
    
    private func feedbackDisplayView(feedback: RecommendationFeedback) -> some View {
        HStack {
            Image(systemName: feedback == .willWatch ? "checkmark.circle.fill" : "xmark.circle.fill")
                .foregroundColor(feedback == .willWatch ? .green : .orange)
            
            Text("You said: \(feedback.description)")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Button("Details") {
                onViewDetails()
            }
            .foregroundColor(.blue)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Preference Row
struct PreferenceRow: View {
    let preference: RecommendationPreference
    let isSelected: Bool
    let onSelect: () -> Void
    
    var body: some View {
        Button(action: onSelect) {
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text(preference.description.components(separatedBy: " ").prefix(3).joined(separator: " "))
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    Text(preference.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }
                
                Spacer()
                
                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(isSelected ? .blue : .gray)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isSelected ? Color.blue.opacity(0.1) : Color(.systemBackground))
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(isSelected ? Color.blue : Color.gray.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Daily Recommendation Preferences View
struct DailyRecommendationPreferencesView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Preferences for daily recommendations")
                    .padding()
                
                Spacer()
            }
            .navigationTitle("Preferences")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Daily Recommendation Detail View
struct DailyRecommendationDetailView: View {
    let recommendation: DailyRecommendation
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Details for \(recommendation.title)")
                    .font(.title)
                    .padding()
                
                Text(recommendation.reason)
                    .padding()
                
                Spacer()
            }
            .navigationTitle(recommendation.title)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    DailyRecommendationsView()
}
