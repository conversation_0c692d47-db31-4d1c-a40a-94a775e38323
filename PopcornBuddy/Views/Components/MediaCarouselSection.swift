import SwiftUI

// MARK: - Media Carousel Section
struct MediaCarouselSection<Content: View>: View {
    let title: String
    let subtitle: String
    let content: Content
    
    init(title: String, subtitle: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.subtitle = subtitle
        self.content = content()
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Section header
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Text(subtitle)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal)
            
            // Horizontal scrolling content
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 16) {
                    content
                }
                .padding(.horizontal)
            }
        }
    }
}

#Preview {
    MediaCarouselSection(
        title: "Sample Section",
        subtitle: "Sample subtitle"
    ) {
        ForEach(0..<5, id: \.self) { _ in
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.3))
                .frame(width: 120, height: 180)
        }
    }
}
