import SwiftUI
import CloudKit
import CoreData

struct CloudKitSyncView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @StateObject private var syncManager = CloudKitSyncManager()
    
    var body: some View {
        NavigationView {
            List {
                // Account Status Section
                Section("iCloud Account") {
                    HStack {
                        Image(systemName: syncManager.accountStatusIcon)
                            .foregroundColor(syncManager.accountStatusColor)
                            .frame(width: 24)
                        
                        VStack(alignment: .leading, spacing: 2) {
                            Text("Account Status")
                                .font(.body)
                            Text(syncManager.accountStatusText)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        if syncManager.accountStatus == .available {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                        }
                    }
                    .padding(.vertical, 4)
                }
                
                // Sync Status Section
                Section("Sync Status") {
                    HStack {
                        Image(systemName: "icloud.and.arrow.up.and.arrow.down")
                            .foregroundColor(.blue)
                            .frame(width: 24)
                        
                        VStack(alignment: .leading, spacing: 2) {
                            Text("Data Sync")
                                .font(.body)
                            Text(syncManager.isInitialSyncComplete ? "Up to date" : "Syncing...")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        if syncManager.isSyncing {
                            ProgressView()
                                .scaleEffect(0.8)
                        } else if syncManager.isInitialSyncComplete {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                        }
                    }
                    .padding(.vertical, 4)
                    
                    if let lastSyncDate = syncManager.lastSyncDate {
                        HStack {
                            Image(systemName: "clock")
                                .foregroundColor(.gray)
                                .frame(width: 24)
                            
                            VStack(alignment: .leading, spacing: 2) {
                                Text("Last Sync")
                                    .font(.body)
                                Text(lastSyncDate, style: .relative)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            
                            Spacer()
                        }
                        .padding(.vertical, 4)
                    }
                }
                
                // Sync Actions Section
                Section("Actions") {
                    Button(action: {
                        Task {
                            await syncManager.forceSyncWithCloudKit()
                        }
                    }) {
                        HStack {
                            Image(systemName: "arrow.clockwise")
                                .foregroundColor(.blue)
                                .frame(width: 24)
                            
                            Text("Force Sync")
                                .foregroundColor(.primary)
                            
                            Spacer()
                            
                            if syncManager.isSyncing {
                                ProgressView()
                                    .scaleEffect(0.8)
                            }
                        }
                    }
                    .disabled(syncManager.isSyncing || syncManager.accountStatus != .available)
                    
                    Button(action: {
                        Task {
                            await syncManager.resetCloudKitData()
                        }
                    }) {
                        HStack {
                            Image(systemName: "trash")
                                .foregroundColor(.red)
                                .frame(width: 24)
                            
                            Text("Reset Cloud Data")
                                .foregroundColor(.red)
                            
                            Spacer()
                        }
                    }
                    .disabled(syncManager.isSyncing)
                }
                
                // Sync Statistics Section
                Section("Statistics") {
                    HStack {
                        Image(systemName: "film")
                            .foregroundColor(.blue)
                            .frame(width: 24)
                        
                        Text("Movies Synced")
                        
                        Spacer()
                        
                        Text("\(syncManager.syncedMoviesCount)")
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Image(systemName: "tv")
                            .foregroundColor(.purple)
                            .frame(width: 24)
                        
                        Text("TV Shows Synced")
                        
                        Spacer()
                        
                        Text("\(syncManager.syncedTVShowsCount)")
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Image(systemName: "clock.arrow.circlepath")
                            .foregroundColor(.orange)
                            .frame(width: 24)
                        
                        Text("Watch History Synced")
                        
                        Spacer()
                        
                        Text("\(syncManager.syncedWatchHistoryCount)")
                            .foregroundColor(.secondary)
                    }
                }
            }
            .navigationTitle("iCloud Sync")
            .navigationBarTitleDisplayMode(.inline)
            .refreshable {
                await syncManager.checkAccountStatus()
                await syncManager.updateSyncStatistics()
            }
        }
        .onAppear {
            Task {
                await syncManager.checkAccountStatus()
                await syncManager.updateSyncStatistics()
            }
        }
    }
}

// MARK: - CloudKit Sync Manager
@MainActor
class CloudKitSyncManager: ObservableObject {
    @Published var accountStatus: CKAccountStatus = .couldNotDetermine
    @Published var isSyncing = false
    @Published var isInitialSyncComplete = false
    @Published var lastSyncDate: Date?
    @Published var syncedMoviesCount = 0
    @Published var syncedTVShowsCount = 0
    @Published var syncedWatchHistoryCount = 0
    
    private let persistenceController = PersistenceController.shared
    
    var accountStatusText: String {
        switch accountStatus {
        case .available:
            return "Signed in and ready to sync"
        case .noAccount:
            return "Not signed in to iCloud"
        case .restricted:
            return "iCloud access restricted"
        case .couldNotDetermine:
            return "Checking account status..."
        case .temporarilyUnavailable:
            return "Temporarily unavailable"
        @unknown default:
            return "Unknown status"
        }
    }
    
    var accountStatusIcon: String {
        switch accountStatus {
        case .available:
            return "icloud"
        case .noAccount:
            return "icloud.slash"
        case .restricted:
            return "exclamationmark.icloud"
        case .couldNotDetermine:
            return "questionmark.circle"
        case .temporarilyUnavailable:
            return "icloud.slash"
        @unknown default:
            return "questionmark.circle"
        }
    }
    
    var accountStatusColor: Color {
        switch accountStatus {
        case .available:
            return .blue
        case .noAccount, .restricted, .temporarilyUnavailable:
            return .red
        case .couldNotDetermine:
            return .orange
        @unknown default:
            return .gray
        }
    }
    
    func checkAccountStatus() async {
        accountStatus = await persistenceController.checkCloudKitAccountStatus()
        
        if accountStatus == .available {
            await updateSyncStatistics()
            checkInitialSyncStatus()
        }
    }
    
    func forceSyncWithCloudKit() async {
        guard accountStatus == .available else { return }
        
        isSyncing = true
        await persistenceController.syncWithCloudKit()
        await updateSyncStatistics()
        lastSyncDate = Date()
        isSyncing = false
    }
    
    func resetCloudKitData() async {
        guard accountStatus == .available else { return }
        
        isSyncing = true
        do {
            try await persistenceController.resetCloudKitData()
            await updateSyncStatistics()
        } catch {
            print("Error resetting CloudKit data: \(error)")
        }
        isSyncing = false
    }
    
    func updateSyncStatistics() async {
        let context = persistenceController.container.viewContext

        await withCheckedContinuation { continuation in
            context.perform {
                // Count synced movies
                let movieRequest: NSFetchRequest<Movie> = Movie.fetchRequest()
                movieRequest.predicate = NSPredicate(format: "ckRecordID != nil")
                self.syncedMoviesCount = (try? context.count(for: movieRequest)) ?? 0

                // Count synced TV shows
                let tvShowRequest: NSFetchRequest<TVShow> = TVShow.fetchRequest()
                tvShowRequest.predicate = NSPredicate(format: "ckRecordID != nil")
                self.syncedTVShowsCount = (try? context.count(for: tvShowRequest)) ?? 0

                // Count synced watch history
                let watchHistoryRequest: NSFetchRequest<WatchHistory> = WatchHistory.fetchRequest()
                watchHistoryRequest.predicate = NSPredicate(format: "ckRecordID != nil")
                self.syncedWatchHistoryCount = (try? context.count(for: watchHistoryRequest)) ?? 0

                continuation.resume()
            }
        }
    }
    
    private func checkInitialSyncStatus() {
        // Check if we have any synced data to determine if initial sync is complete
        isInitialSyncComplete = syncedMoviesCount > 0 || syncedTVShowsCount > 0 || syncedWatchHistoryCount > 0
        
        // Load last sync date from UserDefaults
        lastSyncDate = UserDefaults.standard.object(forKey: "LastCloudKitSyncDate") as? Date
    }
}

#Preview {
    CloudKitSyncView()
}
