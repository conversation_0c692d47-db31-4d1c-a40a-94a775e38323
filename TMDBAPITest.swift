import Foundation
import XCTest
@testable import PopcornBuddy

// MARK: - TMDB API Test Suite
class TMDBAPITest {
    private let tmdbService = TMDBService.shared
    private let configManager = ConfigurationManager.shared
    
    // MARK: - Main Test Function
    func runAllTests() async {
        print("🎬 Starting TMDB API Tests...")
        print("=" * 50)
        
        // Check API key configuration
        await testAPIKeyConfiguration()
        
        // Test basic connectivity
        await testBasicConnectivity()
        
        // Test movie endpoints
        await testMovieEndpoints()
        
        // Test TV show endpoints
        await testTVShowEndpoints()
        
        // Test search functionality
        await testSearchFunctionality()
        
        // Test image URL generation
        testImageURLGeneration()
        
        print("=" * 50)
        print("🎬 TMDB API Tests Complete!")
    }
    
    // MARK: - API Key Configuration Test
    private func testAPIKeyConfiguration() async {
        print("\n🔑 Testing API Key Configuration...")
        
        let apiKey = configManager.tmdbAPIKey
        
        if apiKey == "YOUR_TMDB_API_KEY" || apiKey.isEmpty {
            print("❌ TMDB API key not configured!")
            print("   Please set up your API key using one of these methods:")
            print("   1. Use the in-app API setup screen")
            print("   2. Create a .local.env file with TMDB_API_KEY=your_key")
            print("   3. Store the key in keychain manually")
            return
        }
        
        if apiKey.count < 20 {
            print("⚠️  API key seems too short (length: \(apiKey.count))")
            print("   TMDB API keys are typically 32 characters long")
        } else {
            print("✅ API key configured (length: \(apiKey.count))")
        }
        
        // Test if configuration manager reports valid keys
        if configManager.hasValidAPIKeys() {
            print("✅ Configuration manager reports valid API keys")
        } else {
            print("❌ Configuration manager reports invalid API keys")
        }
    }
    
    // MARK: - Basic Connectivity Test
    private func testBasicConnectivity() async {
        print("\n🌐 Testing Basic TMDB Connectivity...")
        
        do {
            // Test with a simple popular movies request
            let movies = try await tmdbService.fetchPopularMovies()
            
            if movies.isEmpty {
                print("⚠️  API call succeeded but returned no movies")
            } else {
                print("✅ Successfully connected to TMDB API")
                print("   Retrieved \(movies.count) popular movies")
                
                // Show first movie as example
                if let firstMovie = movies.first {
                    print("   Example movie: \(firstMovie.title)")
                }
            }
        } catch {
            print("❌ Failed to connect to TMDB API")
            print("   Error: \(error)")
            
            // Provide specific error guidance
            if let urlError = error as? URLError {
                switch urlError.code {
                case .notConnectedToInternet:
                    print("   → Check your internet connection")
                case .timedOut:
                    print("   → Request timed out, try again")
                case .badServerResponse:
                    print("   → Server returned bad response, check API key")
                default:
                    print("   → Network error: \(urlError.localizedDescription)")
                }
            }
        }
    }
    
    // MARK: - Movie Endpoints Test
    private func testMovieEndpoints() async {
        print("\n🎥 Testing Movie Endpoints...")
        
        // Test trending movies
        do {
            let trendingMovies = try await tmdbService.fetchTrendingMovies()
            print("✅ Trending movies: \(trendingMovies.count) results")
        } catch {
            print("❌ Trending movies failed: \(error)")
        }
        
        // Test popular movies
        do {
            let popularMovies = try await tmdbService.fetchPopularMovies()
            print("✅ Popular movies: \(popularMovies.count) results")
            
            // Test movie details if we have a movie
            if let firstMovie = popularMovies.first {
                do {
                    let details = try await tmdbService.fetchMovieDetails(id: firstMovie.id)
                    print("✅ Movie details for '\(details.title)': runtime \(details.runtime ?? 0) min")
                } catch {
                    print("❌ Movie details failed: \(error)")
                }
            }
        } catch {
            print("❌ Popular movies failed: \(error)")
        }
    }
    
    // MARK: - TV Show Endpoints Test
    private func testTVShowEndpoints() async {
        print("\n📺 Testing TV Show Endpoints...")
        
        // Test trending TV shows
        do {
            let trendingShows = try await tmdbService.fetchTrendingTVShows()
            print("✅ Trending TV shows: \(trendingShows.count) results")
        } catch {
            print("❌ Trending TV shows failed: \(error)")
        }
        
        // Test popular TV shows
        do {
            let popularShows = try await tmdbService.fetchPopularTVShows()
            print("✅ Popular TV shows: \(popularShows.count) results")
            
            // Test TV show details if we have a show
            if let firstShow = popularShows.first {
                do {
                    let details = try await tmdbService.fetchTVShowDetails(id: firstShow.id)
                    print("✅ TV show details for '\(details.name)': \(details.numberOfSeasons) seasons")
                    
                    // Test season details if show has seasons
                    if details.numberOfSeasons > 0 {
                        do {
                            let season = try await tmdbService.fetchTVShowSeason(showId: firstShow.id, seasonNumber: 1)
                            print("✅ Season 1 details: \(season.episodes?.count ?? 0) episodes")
                        } catch {
                            print("❌ Season details failed: \(error)")
                        }
                    }
                } catch {
                    print("❌ TV show details failed: \(error)")
                }
            }
        } catch {
            print("❌ Popular TV shows failed: \(error)")
        }
    }
    
    // MARK: - Search Functionality Test
    private func testSearchFunctionality() async {
        print("\n🔍 Testing Search Functionality...")
        
        let searchQueries = ["Inception", "Breaking Bad", "Marvel", "Star Wars"]
        
        for query in searchQueries {
            do {
                let results = try await tmdbService.searchMulti(query: query)
                print("✅ Search '\(query)': \(results.count) results")
                
                // Show breakdown by media type
                let movies = results.filter { $0.mediaType == "movie" }
                let tvShows = results.filter { $0.mediaType == "tv" }
                let people = results.filter { $0.mediaType == "person" }
                
                if !results.isEmpty {
                    print("   → Movies: \(movies.count), TV: \(tvShows.count), People: \(people.count)")
                }
            } catch {
                print("❌ Search '\(query)' failed: \(error)")
            }
        }
    }
    
    // MARK: - Image URL Generation Test
    private func testImageURLGeneration() {
        print("\n🖼️  Testing Image URL Generation...")
        
        let testPosterPath = "/example_poster.jpg"
        let testBackdropPath = "/example_backdrop.jpg"
        
        // Test poster URLs
        if let posterURL = tmdbService.posterURL(for: testPosterPath, size: .w500) {
            print("✅ Poster URL generated: \(posterURL)")
        } else {
            print("❌ Failed to generate poster URL")
        }
        
        // Test backdrop URLs
        if let backdropURL = tmdbService.backdropURL(for: testBackdropPath, size: .w1280) {
            print("✅ Backdrop URL generated: \(backdropURL)")
        } else {
            print("❌ Failed to generate backdrop URL")
        }
        
        // Test with nil paths
        let nilPosterURL = tmdbService.posterURL(for: nil)
        let nilBackdropURL = tmdbService.backdropURL(for: nil)
        
        if nilPosterURL == nil && nilBackdropURL == nil {
            print("✅ Nil path handling works correctly")
        } else {
            print("❌ Nil path handling failed")
        }
    }
}

// MARK: - String Extension for Repeat
extension String {
    static func * (string: String, count: Int) -> String {
        return String(repeating: string, count: count)
    }
}

// MARK: - Standalone Test Runner
func runTMDBAPITest() async {
    let test = TMDBAPITest()
    await test.runAllTests()
}
